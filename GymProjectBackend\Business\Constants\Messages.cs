﻿using Core.Utilities.Security.JWT;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Business.Constants
{
    public static class Messages
    {
        public static string PhoneNumberAlreadyExist="Bu telefon numarası sistemde zaten kayıtlı.";
        public static string UserAdded="Kullanıcı eklendi";
        public static string UserUpdated="Kullanıcı güncellendi";
        public static string UserDeleted="Kullanıcı silindi";
        public static string CompanyAdded="Şirket eklendi";
        public static string CompanyDeleted="Şirket silindi";
        public static string CompanyUpdated="Şirket güncellendi";
        public static string CompanyAdressAdded="Şirket Adresi eklendi";
        public static string CompanyAdressDeleted= "Şirket Adresi silindi";
        public static string CompanyAdressUpdated = "Şirket Adresi güncellendi";
        public static string MemberAdded="Müşteri eklendi";
        public static string MemberDeleted="Müşteri silindi";
        public static string MemberUpdated="Müşteri güncellendi";
        public static string MemberLinkedWithUser="Müşteri, mevcut kullanıcı hesabı ile ilişkilendirildi";
        public static string MembershipTypeAdded="Üyelik türü eklendi";
        public static string MembershipTypeDeleted="Üyelik türü silindi";
        public static string MembershipTypeUpdated="Üyelik türü güncellendi";
        public static string MembershipAdded="Üyelik eklendi";
        public static string MembershipDeleted="Üyelik silindi";
        public static string MembershipUpdated="Üyelik güncellendi";
        public static string UserNotFound = "Kullanıcı adı veya şifre hatalı";
        public static string PasswordError = "Şifre hatalı";
        public static string SuccessfulLogin = "Giriş başarılı";
        public static string UserAlreadyExists = "Bu kullanıcı zaten mevcut";
        public static string UserRegistered = "Kullanıcı başarıyla kaydedildi";
        public static string MemberRegistered = "Üye başarıyla kaydedildi";
        public static string AuthorizationDenied = "Yetkiniz yok";
        public static string PaymentAdded="Ödeme alındı";
        public static string PaymentDeleted="Ödeme silindi";
        public static string PaymentUpdated="Ödeme güncellendi";
        public static string CompanyGetAll ="Şirket bilgileri Listelendi";
        public static string CompanyUserGetAll ="Şirket üye bilgileri listelendi";
        public static string CompanyUserDetailsListed ="Salon sahipleri bilgileri listelendi";
        public static string UserCompanyAdded ="Şahıs şirketi eklendi";
        public static string UserCompanyUpdated = "Şahıs şirketi güncellendi";
        public static string UserCompanyDeleted = "Şahıs şirketi silindi";
        public static string EntryHistoryAdded="Giriş, Çıkış bilgisi kaydedildi";
        public static string EntryExitHistoryUpdated="Giriş, Çıkış bilgisi güncellendi";
        public static string EntryExitHistoryDeleted="Giriş, Çıkış bilgisi silindi";
        public static string MembershipNotFound="Üyelik Bulunamadı";
        public static string CompanyDataDeleted="Şirket verileri silindi";
        public static string MemberNotFound = "Üye bulunamadı";
        public static string MembershipExpired ="Üyelik süresi dolmuş";
        public static string MembershipNotFoundOrExpired = "Üyelik bulunamadı veya süresi dolmuş.";
        public static string EntryLogged = "Giriş kaydedildi.";
        public static string ExitLogged = "Çıkış kaydedildi.";
        public static string PaymentUpdateFailed ="Ödeme Güncellenemedi";
        public static string ProductAdded = "Ürün başarıyla eklendi.";
        public static string ProductDeleted = "Ürün başarıyla silindi.";
        public static string ProductUpdated = "Ürün başarıyla güncellendi.";
        public static string ProductNotFound = "Ürün bulunamadı.";
        public static string ProductsListed = "Ürünler başarıyla listelendi.";
        public static string TransactionAdded = "İşlem başarıyla gerçekleştirildi.";
        public static string InsufficientBalance = "Yetersiz bakiye.";
        public static string InvalidTransactionType ="Geçersiz işlem türü";
        public static string PaymentNotFound ="Üye Bulunamadı";
        public static string InvalidPaymentAmount ="Ödeme miktarı geçersiz";
        public static string OperationClaimAdded="Rol eklendi";
        public static string OperationClaimDeleted="Rol Silindi";
        public static string OperationClaimsListed="Roller listelendi";
        public static string OperationClaimUpdated="Rol güncellendi";
        public static string UserOperationClaimAdded="Kullanıcıya Rol Eklendi";
        public static string UserOperationClaimDeleted="Kullanıcının Rolü Silindi";
        public static string UserOperationClaimsListed="Kullanıcı Rolleri Listelendi";
        public static string UserOperationClaimUpdated="Kullanıcı Rolleri Güncellendi";
        public static string TransactionNotFound ="İşlem Bulunamadı";
        public static string TransactionUpdated="İşlem Güncellendi";
        public static string TokensRefreshed="Token Yenilendi";
        public static string InvalidIPAddress ="Geçersiz ip adresi";
        public static string ExpiredRefreshToken="Tokenın süresi doldu";
        public static string InvalidRefreshToken="Geçersiz Token";
        public static string RefreshTokenRevoked="Token silindi";
        public static string LogoutSuccessful = "Çıkış işlemi başarılı";
        public static string DeviceNotFound = "Cihaz bulunamadı";
        public static string MaximumDeviceLimitReached = "Maksimum cihaz limitine ulaşıldı";
        public static string DeviceRevoked = "Cihaz oturumu sonlandırıldı";
        public static string TokensCreated ="Tokenlar oluşturuldu";
        public static string MembershipFrozen = "Üyelik donduruldu";
        public static string MembershipUnfrozen = "Üyelik dondurma işlemi kaldırıldı";
        public static string MembershipAlreadyFrozen = "Üyelik zaten dondurulmuş durumda";
        public static string MembershipNotFrozen = "Üyelik dondurulmuş değil";
        public static string FreezeDaysInvalid = "Dondurma süresi 1-365 gün arasında olmalıdır";
        public static string UserCompanyNotFound = "Kullanıcıya ait şirket bulunamadı";
        public static string UserCompanyAccessDenied = "Bu şirkete erişim yetkiniz yok";
        public static string UserCompanyNotActive = "Bu şirket aktif değil";
        public static string CompanyChanged = "Aktif şirket değiştirildi";
        public static string CompanyNotFound = "Şirket bulunamadı";

        // Expense Messages
        public static string ExpenseAdded = "Gider başarıyla eklendi.";
        public static string ExpenseUpdated = "Gider başarıyla güncellendi.";
        public static string ExpenseDeleted = "Gider başarıyla silindi.";
        public static string ExpenseNotFound = "Gider bulunamadı.";
        public static string AmountMustBePositive = "Tutar 0'dan büyük olmalıdır.";

        // Workout Program Messages
        public static string WorkoutProgramAdded = "Antrenman programı başarıyla oluşturuldu.";
        public static string WorkoutProgramUpdated = "Antrenman programı başarıyla güncellendi.";
        public static string WorkoutProgramDeleted = "Antrenman programı başarıyla silindi.";
        public static string WorkoutProgramNotFound = "Antrenman programı bulunamadı.";
        public static string WorkoutProgramsListed = "Antrenman programları başarıyla listelendi.";
        public static string WorkoutProgramDetailRetrieved = "Antrenman programı başarıyla getirildi.";
        public static string WorkoutProgramNameExists = "Bu isimde bir antrenman programı zaten mevcut.";
        public static string MaxDayCountExceeded = "Bir antrenman programı maksimum 7 gün olabilir.";
        public static string MinDayCountRequired = "Bir antrenman programı en az 1 gün olmalıdır.";
        public static string DayNumbersMustBeUnique = "Gün numaraları benzersiz olmalıdır.";
        public static string DayNumbersInvalidRange = "Gün numaraları 1-7 arasında olmalıdır.";
        public static string AtLeastOneWorkoutDayRequired = "En az bir gün egzersiz günü olmalıdır. Tüm günler dinlenme günü olamaz.";
    }
}
